package main

import (
	"fmt"
	"time"
)

// 简化的结构体用于测试
type TenantPayment struct {
	Date   time.Time
	Amount float64
}

type Lease struct {
	StartDate    string
	EndDate      string
	RentAmount   float64
	OwingBalance float64
}

// 测试用的 calculatePaymentHistory 函数
func calculatePaymentHistory(payments []*TenantPayment, lease *Lease, reportMonth time.Time) string {
	history := make([]string, 24)

	// 解析租约开始日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return "BBBBBBBBBBBBBBBBBBBBBBBB"
	}

	// 解析租约结束日期（如果有）
	// leaseEnd := time.Time{}
	// if lease.EndDate != "" {
	// 	leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
	// 	if err != nil {
	// 		return "BBBBBBBBBBBBBBBBBBBBBBBB"
	// 	}
	// }

	// 创建按月份分组的付款记录
	paymentsByMonth := make(map[string]float64)
	for _, payment := range payments {
		monthKey := payment.Date.Format("2006-01")
		paymentsByMonth[monthKey] += payment.Amount
	}

	// 初始化所有位置为 B
	for i := 0; i < 24; i++ {
		history[i] = "B"
	}

	// 计算租约开始月份
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())

	// 先从最旧的月份开始计算（从租约开始月份到报告月份）
	// 然后反向填充到history数组中

	// 计算从租约开始到报告月份的所有月份
	months := make([]time.Time, 0)
	current := leaseStartMonth
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	for current.Before(reportMonthStart) || current.Equal(reportMonthStart) {
		months = append(months, current)
		current = current.AddDate(0, 1, 0)
	}

	// 计算连续逾期月数（从报告月开始向前计算）
	consecutiveOverdueMonths := 0

	// 逐月检查付款历史（从报告月份向前检查）
	for i := 0; i < 24; i++ {
		// 计算当前检查的月份（从报告月份开始向前）
		checkMonth := reportMonth.AddDate(0, -i, 0)
		monthStart := time.Date(checkMonth.Year(), checkMonth.Month(), 1, 0, 0, 0, 0, checkMonth.Location())

		// 如果月份在租约开始前，标记为 B
		if monthStart.Before(leaseStartMonth) {
			history[i] = "B"
			continue
		}

		// 获取该月的付款总额
		monthKey := checkMonth.Format("2006-01")
		monthlyPayment := paymentsByMonth[monthKey]
		expectedRent := lease.RentAmount

		// 根据付款情况确定状态
		if monthlyPayment >= expectedRent {
			history[i] = "0"             // 足额付款
			consecutiveOverdueMonths = 0 // 重置连续逾期计数
		} else {
			// 未足额付款，增加连续逾期月数
			consecutiveOverdueMonths++

			// 根据连续逾期月数确定状态
			switch {
			case consecutiveOverdueMonths == 1:
				history[i] = "1" // 30-59天逾期
			case consecutiveOverdueMonths == 2:
				history[i] = "2" // 60-89天逾期
			case consecutiveOverdueMonths >= 3:
				history[i] = "3" // 90+天逾期
			}
		}
	}

	// 直接返回结果，不需要反转
	result := ""
	for _, h := range history {
		result += h
	}
	return result
}

func main() {
	// 测试案例1: 5月1日入住，7月份报告
	lease1 := &Lease{
		StartDate:  "2025-05-01",
		EndDate:    "",
		RentAmount: 1500,
	}

	// 模拟一些付款记录
	payments1 := []*TenantPayment{
		{Date: time.Date(2025, 5, 15, 0, 0, 0, 0, time.UTC), Amount: 1500}, // 5月足额付款
		{Date: time.Date(2025, 6, 15, 0, 0, 0, 0, time.UTC), Amount: 750},  // 6月部分付款
		// 7月无付款
	}

	reportMonth1 := time.Date(2025, 7, 31, 0, 0, 0, 0, time.UTC)
	result1 := calculatePaymentHistory(payments1, lease1, reportMonth1)

	fmt.Printf("测试案例1 (5月入住，7月报告):\n")
	fmt.Printf("期望: 21BBBBBBBBBBBBBBBBBBBBBB (7月无付款=1, 6月部分付款=1, 5月足额=0, 其余=B)\n")
	fmt.Printf("实际: %s\n", result1)
	fmt.Printf("前3位: %s\n\n", result1[:3])

	// 测试案例2: 11月1日入住，3月份报告 (对应示例文件)
	lease2 := &Lease{
		StartDate:  "2024-11-01",
		EndDate:    "",
		RentAmount: 1500,
	}

	payments2 := []*TenantPayment{
		// 11月无付款, 12月无付款, 1月无付款, 2月无付款, 3月无付款
	}

	reportMonth2 := time.Date(2025, 3, 31, 0, 0, 0, 0, time.UTC)
	result2 := calculatePaymentHistory(payments2, lease2, reportMonth2)

	fmt.Printf("测试案例2 (11月入住，3月报告，无付款):\n")
	fmt.Printf("期望: 321BBBBBBBBBBBBBBBBBBBBB (3月=3, 2月=2, 1月=1, 其余=B)\n")
	fmt.Printf("实际: %s\n", result2)
	fmt.Printf("前5位: %s\n", result2[:5])
}
